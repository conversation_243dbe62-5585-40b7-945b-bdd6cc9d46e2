<script lang="ts">
	import RichTextArea from '$lib/components/common/RichTextArea.svelte';
	import EmailCompositionModal from '$lib/components/conversation/EmailCompositionModal.svelte';
	
	let richTextContent = '';
	let showModal = false;
	let testResults: string[] = [];
	
	// Test composition data
	let compositionData = {
		action: 'compose' as const,
		content: '',
		subject: 'Test Subject',
		to: ['<EMAIL>'],
		cc: [],
		bcc: []
	};
	
	function handleRichTextUpdate(event: CustomEvent) {
		richTextContent = event.detail.editor.getHTML();
		addTestResult(`RichText content updated: ${richTextContent.substring(0, 50)}...`);
	}
	
	function testContentPersistence() {
		addTestResult('Testing content persistence...');
		// Simulate user typing and then clicking away
		richTextContent = '<p>Test content that should persist</p>';
		addTestResult(`Set content to: ${richTextContent}`);
		
		// Simulate blur by clicking elsewhere
		setTimeout(() => {
			addTestResult(`Content after simulated blur: ${richTextContent}`);
		}, 100);
	}
	
	function openEmailModal() {
		compositionData.content = richTextContent;
		showModal = true;
		addTestResult('Opened email modal with content: ' + richTextContent);
	}
	
	function handleEmailSend(event: CustomEvent) {
		addTestResult('Email send attempted with content: ' + event.detail.content);
		showModal = false;
	}
	
	function handleEmailCancel() {
		showModal = false;
		addTestResult('Email modal cancelled');
	}
	
	function clearResults() {
		testResults = [];
	}
	
	function addTestResult(message: string) {
		testResults = [...testResults, `${new Date().toLocaleTimeString()}: ${message}`];
	}
</script>

<div class="container mx-auto p-8">
	<h1 class="text-2xl font-bold mb-6">RichTextArea & EmailCompositionModal Test</h1>
	
	<!-- Test Controls -->
	<div class="mb-6 space-x-4">
		<button 
			class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
			on:click={testContentPersistence}
		>
			Test Content Persistence
		</button>
		<button 
			class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
			on:click={openEmailModal}
		>
			Open Email Modal
		</button>
		<button 
			class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
			on:click={clearResults}
		>
			Clear Results
		</button>
	</div>
	
	<!-- RichTextArea Test -->
	<div class="mb-8">
		<h2 class="text-xl font-semibold mb-4">RichTextArea Test</h2>
		<div class="border border-gray-300 rounded-lg p-4">
			<p class="mb-2 text-sm text-gray-600">
				Type some content, then click outside the editor. The content should persist.
			</p>
			<div class="h-64">
				<RichTextArea 
					bind:content={richTextContent}
					on:update={handleRichTextUpdate}
					expandToFill={true}
				/>
			</div>
		</div>
		<div class="mt-2 p-2 bg-gray-100 rounded">
			<strong>Current Content:</strong> {richTextContent || '(empty)'}
		</div>
	</div>
	
	<!-- Test Results -->
	<div class="mb-8">
		<h2 class="text-xl font-semibold mb-4">Test Results</h2>
		<div class="bg-gray-50 border rounded-lg p-4 max-h-64 overflow-y-auto">
			{#each testResults as result}
				<div class="text-sm font-mono mb-1">{result}</div>
			{:else}
				<div class="text-gray-500 italic">No test results yet</div>
			{/each}
		</div>
	</div>
</div>

<!-- Email Composition Modal -->
<EmailCompositionModal
	bind:isOpen={showModal}
	{compositionData}
	on:send={handleEmailSend}
	on:cancel={handleEmailCancel}
	on:close={handleEmailCancel}
/>

<style>
	.container {
		max-width: 1200px;
	}
</style>
